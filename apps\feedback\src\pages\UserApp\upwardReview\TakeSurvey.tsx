import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router';
import { Alert, AlertDescription } from '@repo/ui/components/alert';
import { Button } from '@repo/ui/components/button';
import { USER_ROUTES } from '@/app.routes';
import QuestionRenderer from '@/components/survey/questions/QuestionRenderer';
import SubmitConfirmation from './components/SubmitConfirmation';
import SurveySidebar from './components/SurveySidebar';
import { useSurveyData } from './hooks/useSurveyData';

const TakeSurvey: React.FC = () => {
  const { id, surveyId } = useParams<{ id: string; surveyId: string }>();
  const navigate = useNavigate();

  // Use custom hook for survey data management
  const {
    isLoading,
    isSaving,
    error,
    meta,
    sections,
    currentSection,
    questions,
    allQuestions,
    analytics,
    faqs,
    fetchSurveyData,
    loadQuestionsForSection,
    updateQuestionResponse,
    submitSurvey,
    setCurrentSection,
    getCompletionPercentage
  } = useSurveyData();

  // Local state for UI
  const [confirmSubmit, setConfirmSubmit] = useState(false);
  const [showFaqDialog, setShowFaqDialog] = useState(false);

  // Initialize survey data on component mount
  useEffect(() => {
    if (id && surveyId) {
      fetchSurveyData(id, surveyId);
    }
  }, [id, surveyId, fetchSurveyData]);

  // Handle section selection
  const handleSectionSelect = async (section: any) => {
    setCurrentSection(section);
    setConfirmSubmit(false);
    await loadQuestionsForSection(section.id);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Handle navigation
  const handleNext = () => {
    if (!currentSection || !sections) return;

    const currentIndex = sections.findIndex(s => s.id === currentSection.id);
    if (currentIndex < sections.length - 1) {
      setCurrentSection(sections[currentIndex + 1]);
    } else if (meta?.canSubmit) {
      setConfirmSubmit(true);
    }
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handlePrevious = () => {
    if (!currentSection || !sections) return;

    const currentIndex = sections.findIndex(s => s.id === currentSection.id);
    if (currentIndex > 0) {
      setCurrentSection(sections[currentIndex - 1]);
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  const handleBack = () => {
    navigate(USER_ROUTES().dashboard.upwardReview.getPairingsUrl(meta?.indexId || ''));
  };

  // Handle survey submission
  const handleSubmit = async () => {
    if (!id) return;

    try {
      await submitSurvey(id);
      navigate(USER_ROUTES().dashboard.upwardReview.getPairingsUrl(meta?.indexId || ''));
    } catch (err) {
      // Error is already handled in the hook
    }
  };

  // Handle question updates using the hook
  const handleQuestionUpdate = async (questionId: string, response: any, _responseId?: string, shouldDelete?: boolean) => {
    await updateQuestionResponse(questionId, response, undefined, shouldDelete);
  };

  // Handle comment updates separately
  const handleCommentUpdate = async (questionId: string, comment: string, _commentId?: string, _onNewResponseId?: (newId: string) => void) => {
    // For comment updates, we need to call updateQuestionResponse with the current response value
    // and the feedback parameter set to the comment
    const question = questions.find(q => q.id === questionId);
    const currentResponse = question?.response?.value;
    await updateQuestionResponse(questionId, currentResponse, comment, false);
  };

  // Error state
  if (error) {
    return (
      <div className="p-6">
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  // Submit confirmation state
  if (confirmSubmit) {
    return (
      <SubmitConfirmation
        meta={meta}
        analytics={analytics}
        onBack={() => setConfirmSubmit(false)}
        onSubmit={handleSubmit}
        isLoading={isSaving}
      />
    );
  }

  return (
    <div className="h-full bg-gray-50 dark:bg-gray-900 w-full flex flex-col">
      {/* Main content area with sidebar and content */}
      <div className="flex flex-1 min-h-0">
        {/* Fixed Sidebar */}
        <SurveySidebar
          meta={meta}
          sections={sections}
          currentSection={currentSection}
          analytics={analytics}
          faqs={faqs}
          isLoading={isLoading}
          showFaqDialog={showFaqDialog}
          allQuestions={allQuestions}
          onSectionSelect={handleSectionSelect}
          onBack={handleBack}
          onFaqDialogChange={setShowFaqDialog}
        />

        {/* Main Content Area */}
        <div className="flex-1 bg-white dark:bg-gray-800 flex flex-col min-h-0 relative">
          {isLoading ? (
            <div className="p-6 space-y-6">
              <div className="h-8 bg-gray-200 dark:bg-gray-600 rounded animate-pulse w-3/4"></div>
              <div className="h-32 bg-gray-200 dark:bg-gray-600 rounded animate-pulse w-full"></div>
              <div className="h-32 bg-gray-200 dark:bg-gray-600 rounded animate-pulse w-full"></div>
            </div>
          ) : !currentSection ? (
            <div className="p-6">
              <p className="text-gray-600 dark:text-gray-400">No sections available</p>
            </div>
          ) : (
            <>
              {/* Fixed Header - Orange bar like legacy app */}
              <div className="bg-orange-400 dark:bg-orange-600 px-6 py-2 border-b dark:border-gray-700 flex-shrink-0 sticky top-0 z-10">
                <div className="flex justify-between items-center">
                  <h1 className="text-lg font-medium text-white">
                    {meta?.title || 'Upward Review'}
                  </h1>
                  <div className="flex items-center space-x-4 text-sm text-white">
                    <span>{getCompletionPercentage()}% Completed</span>
                    <span>•</span>
                    <span>
                      {isSaving ? 'Saving...' : `Saved ${meta?.lastModified || 'Just now'}`}
                    </span>
                  </div>
                </div>
              </div>

              {/* Fixed Section Header - Light blue bar like legacy */}
              <div className="dark:bg-blue-800 px-6 py-2 dark:border-gray-700 flex-shrink-0 sticky top-[42px] z-10">
                <h2 className="text-lg font-medium text-gray-800 dark:text-white">{currentSection.title}</h2>
              </div>

              {/* Scrollable Questions Content */}
              <div className="flex-1 overflow-y-auto">
                <div className="p-6 space-y-8">
                  {questions.length === 0 ? (
                    <p className="text-gray-600 dark:text-gray-400">No questions in this section</p>
                  ) : (
                    questions.map((question, index) => (
                      <QuestionRenderer
                        key={question.id}
                        question={question}
                        questionNumber={index + 1}
                        onResponseUpdate={(questionId: string, value: any, responseId?: string, shouldDelete?: boolean) =>
                          handleQuestionUpdate(questionId, value, responseId, shouldDelete)
                        }
                        onCommentUpdate={handleCommentUpdate}
                      />
                    ))
                  )}
                </div>
              </div>

              {/* Fixed Footer */}
              <div className="bg-white dark:bg-gray-800 border-t dark:border-gray-700 flex-shrink-0">
                {/* Navigation Footer */}
                <div className="px-6 py-2">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center space-x-4">
                      {/* Back button for section navigation */}
                      {sections && currentSection && sections.findIndex(s => s.id === currentSection.id) > 0 && (
                        <Button
                          onClick={handlePrevious}
                          variant="outline"
                          className="px-6"
                        >
                          Back
                        </Button>
                      )}
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        {currentSection && questions.length > 0 && (
                          <span>
                            {questions.filter(q =>
                              !q.response || q.response.value === undefined || q.response.value === null || q.response.value === ''
                            ).length} Questions left in the section
                          </span>
                        )}
                      </div>
                    </div>
                    <Button
                      onClick={handleNext}
                      className="bg-blue-600 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700 px-8"
                    >
                      {sections && currentSection &&
                       sections.findIndex(s => s.id === currentSection.id) === sections.length - 1
                        ? 'Submit my response'
                        : 'Next'
                      }
                    </Button>
                  </div>
                </div>

                {/* Privacy/Terms Footer */}
                <div className="border-t dark:border-gray-700 px-6 py-3 bg-gray-50 dark:bg-gray-900">
                  <div className="flex justify-center items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                    <a
                      href="/terms"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
                    >
                      Terms
                    </a>
                    <span>•</span>
                    <a
                      href="/privacy"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
                    >
                      Privacy Policy
                    </a>
                    <span>•</span>
                    <a
                      href="/confidentiality"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
                    >
                      Confidentiality
                    </a>
                    <span>•</span>
                    <a
                      href="/contact"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
                    >
                      Contact Us
                    </a>
                    <span>•</span>
                    <span className="text-gray-400 dark:text-gray-500">
                      Unmatched © {new Date().getFullYear()}
                    </span>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default TakeSurvey;
