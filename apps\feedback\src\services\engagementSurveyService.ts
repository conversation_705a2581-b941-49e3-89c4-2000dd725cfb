import axiosInstance from '@/lib/axios';

export interface SurveyResponse {
  id: string;
  survey: string;
  resourcetype: string;
}

export interface SurveyQuestion {
  id: string;
  title: string;
  label: string;
  type: string;
  resourcetype: string;
  section: number;
  mandatory: boolean;
  collect_feedback: boolean;
  is_reverse_scale: boolean;
  options?: any;
  response?: {
    id: string;
    value: any;
    is_valid: boolean;
  };
  feedback?: string;
  commentId?: string;
}

export interface SurveySection {
  id: number;
  title: string;
  questions: SurveyQuestion[];
  isDemographic?: boolean;
}

export interface SurveyMeta {
  title: string;
  endDate: string;
  canSubmit: boolean;
  nextTitle: string;
  buttonType: string;
  hideBack: boolean;
  hideNoBasisOption?: boolean;
}

export interface EngagementSurveyData {
  meta: SurveyMeta;
  sections: SurveySection[];
  questions: SurveyQuestion[];
  responseId: string;
  versionId: string;
  demographicsId?: string;
  completion: number;
}

/**
 * Get survey version ID for the given survey index
 */
export const getSurveyVersionId = async (surveyIndexId: string): Promise<{ versionId: string; demographicsId?: string }> => {
  const response = await axiosInstance.get(`/survey/survey-index/${surveyIndexId}`);
  const data = response.data;

  if (data.surveys && data.surveys.length) {
    const [version] = data.surveys;
    return {
      versionId: version.id,
      demographicsId: data.demographic_id,
    };
  }

  return {
    versionId: '',
    demographicsId: data.demographic_id,
  };
};

/**
 * Create a new survey response for engagement survey
 */
export const createSurveyResponse = async (surveyVersionId: string): Promise<SurveyResponse> => {
  const response = await axiosInstance.post('/survey/survey-response/', {
    survey: surveyVersionId,
    resourcetype: 'SurveyResponseEngagement',
  });
  
  return response.data;
};

/**
 * Get survey structure and questions
 */
export const getSurveyStructure = async (surveyVersionId: string) => {
  const response = await axiosInstance.get(`/survey/survey/${surveyVersionId}?survey=${surveyVersionId}`);
  return response.data;
};

/**
 * Get existing responses for a survey response (sections and components)
 */
export const getSurveyResponses = async (responseId: string) => {
  const response = await axiosInstance.get(`/survey/survey-response/${responseId}`);
  return response.data;
};

/**
 * Transform raw survey data into structured format
 */
export const transformSurveyData = (_surveyData: any, responsesData: any, responseId: string, versionId: string, demographicsId?: string): EngagementSurveyData => {
  // The responsesData comes from /survey/survey-response/{id} and contains survey_detail with sections and components
  const surveyDetail = responsesData.survey_detail || {};
  const { sections } = surveyDetail;

  // Create a map of responses by question ID from question_responses
  const responseMap = new Map();
  if (responsesData.question_responses) {
    responsesData.question_responses.forEach((response: any) => {
      responseMap.set(response.question, response);
    });
  }

  // Create a map of comment responses by question ID for QuestionInput types
  const commentMap = new Map();
  if (responsesData.comment_responses) {
    responsesData.comment_responses.forEach((comment: any) => {
      commentMap.set(comment.question, comment);
    });
  }

  // Transform sections and their components (questions)
  const transformedSections: SurveySection[] = (sections || []).map((section: any) => {
    const sectionQuestions: SurveyQuestion[] = (section.components || [])
      .filter((component: any) => component.resourcetype !== "QuestionNumber") // Filter out number questions
      .map((component: any) => {
        // For QuestionInput types, use comment responses instead of question responses
        let response;
        if (component.resourcetype === 'QuestionInput') {
          response = commentMap.get(component.id);
        } else {
          response = responseMap.get(component.id);
        }

        // Get comment data for questions that collect feedback
        const comment = commentMap.get(component.id);

        return {
          id: component.id,
          title: component.label,
          label: component.label,
          type: component.resourcetype,
          resourcetype: component.resourcetype,
          section: section.id,
          mandatory: component.mandatory,
          collect_feedback: component.collect_feedback,
          is_reverse_scale: component.is_reverse_scale,
          options: component.options || {},
          response: response ? {
            id: response.id,
            value: response.value,
            is_valid: response.is_valid,
          } : undefined,
          // Add comment data for questions that collect feedback
          feedback: comment?.value,
          commentId: comment?.id,
        };
      });

    return {
      id: section.id,
      title: section.name,
      questions: sectionQuestions,
    };
  });

  // Flatten all questions for overall stats
  const allQuestions = transformedSections.flatMap(section => section.questions);

  // Calculate completion percentage
  const totalQuestions = allQuestions.length;
  const answeredQuestions = allQuestions.filter(q => {
    const value = q.response?.value;
    return value !== undefined && value !== null && value !== '';
  }).length;
  const completion = totalQuestions > 0 ? Math.round((answeredQuestions / totalQuestions) * 100) : 0;

  // Get survey metadata from responsesData
  const surveyMeta = responsesData.index || {};

  return {
    meta: {
      title: surveyMeta.title || 'Engagement Survey',
      endDate: surveyMeta.deadline || '',
      canSubmit: completion === 100,
      nextTitle: 'Next',
      buttonType: 'primary',
      hideBack: false,
      hideNoBasisOption: surveyDetail.hide_no_basis_option || false,
    },
    sections: transformedSections,
    questions: allQuestions,
    responseId,
    versionId,
    demographicsId,
    completion,
  };
};

/**
 * Get complete engagement survey data
 */
export const getEngagementSurveyData = async (surveyIndexId: string): Promise<EngagementSurveyData> => {
  try {
    // Step 1: Get survey version ID
    const { versionId, demographicsId } = await getSurveyVersionId(surveyIndexId);

    if (!versionId) {
      throw new Error('No Survey Version Present');
    }

    // Step 2: Create survey response
    const surveyResponse = await createSurveyResponse(versionId);

    // Step 3: Get survey structure and existing responses in parallel
    const [surveyStructure, existingResponses] = await Promise.all([
      getSurveyStructure(versionId),
      getSurveyResponses(surveyResponse.id),
    ]);

    // Step 4: Transform regular survey data
    let surveyData = transformSurveyData(
      surveyStructure,
      existingResponses,
      surveyResponse.id,
      versionId,
      demographicsId
    );

    // Step 5: If demographics exist, fetch and append demographics data
    if (demographicsId) {
      try {
        const demographicsData = await getDemographicsData(demographicsId, surveyResponse.id);

        // Append demographics sections to regular sections
        surveyData = {
          ...surveyData,
          sections: [...surveyData.sections, ...demographicsData.sections],
          questions: [...surveyData.questions, ...demographicsData.questions],
        };

        // Recalculate completion percentage with demographics included
        const totalQuestions = surveyData.questions.length;
        const answeredQuestions = surveyData.questions.filter(q => {
          const value = q.response?.value;
          return value !== undefined && value !== null && value !== '';
        }).length;
        const completion = totalQuestions > 0 ? Math.round((answeredQuestions / totalQuestions) * 100) : 0;

        surveyData = {
          ...surveyData,
          completion,
          meta: {
            ...surveyData.meta,
            canSubmit: completion === 100,
          },
        };
      } catch (demographicsError) {
        console.error('Error fetching demographics data:', demographicsError);
        // Continue without demographics if there's an error
      }
    }

    return surveyData;
  } catch (error) {
    console.error('Error fetching engagement survey data:', error);
    throw error;
  }
};

/**
 * Save or update a question response
 */
export const saveQuestionResponse = async (
  responseId: string,
  questionId: string,
  value: any,
  existingResponseId?: string
): Promise<any> => {
  const payload = {
    survey_response: responseId,
    question: questionId,
    value: value,
  };

  if (existingResponseId) {
    // Update existing response
    return axiosInstance.patch(`/survey/question-response/${existingResponseId}/`, payload);
  } else {
    // Create new response
    return axiosInstance.post('/survey/question-response/', payload);
  }
};

/**
 * Delete a question response
 */
export const deleteQuestionResponse = async (responseId: string): Promise<any> => {
  return axiosInstance.delete(`/survey/question-response/${responseId}/`);
};

/**
 * Save or update a demographics question response
 */
export const saveDemographicsResponse = async (
  responseId: string,
  questionId: string,
  value: any,
  existingResponseId?: string
): Promise<any> => {
  const payload = {
    survey_response: responseId,
    question: questionId,
    value: value,
  };

  if (existingResponseId) {
    // Update existing demographics response
    return axiosInstance.patch(`/survey/demographic-response/${existingResponseId}/`, payload);
  } else {
    // Create new demographics response
    return axiosInstance.post('/survey/demographic-response/', payload);
  }
};

/**
 * Delete a demographics response
 */
export const deleteDemographicsResponse = async (responseId: string): Promise<any> => {
  return axiosInstance.delete(`/survey/demographic-response/${responseId}/`);
};

/**
 * Save or update a comment response
 */
export const saveCommentResponse = async (
  responseId: string,
  questionId: string,
  comment: string,
  existingCommentId?: string
): Promise<any> => {
  const payload = {
    survey_response: responseId,
    question: questionId,
    value: comment,
  };

  if (existingCommentId) {
    // Update existing comment
    return axiosInstance.patch(`/survey/comment-response/${existingCommentId}/`, payload);
  } else {
    // Create new comment
    return axiosInstance.post('/survey/comment-response/', payload);
  }
};

/**
 * Get demographics responses for a survey response
 */
export const getDemographicResponses = async (surveyResponseId: string) => {
  const response = await axiosInstance.get('/survey/demographic-response/', {
    params: {
      survey_response: surveyResponseId,
    },
  });
  return response.data;
};

/**
 * Get demographics data structure and responses
 *
 * Demographics are special sections that appear only in engagement surveys.
 * They are fetched separately from regular survey sections and have their own API endpoints:
 * - /survey/demographics/{id}/ - Gets demographics structure
 * - /survey/demographic-response/ - Gets/saves demographics responses
 *
 * Demographics sections are marked with isDemographic: true and appear at the bottom
 * of the sections list in the sidebar, similar to the old app implementation.
 */
export const getDemographicsData = async (demographicsId: string, surveyResponseId: string) => {
  try {
    // First get the demographics structure
    const structureResponse = await axiosInstance.get(`/survey/demographics/${demographicsId}/`);
    const demographicsData = structureResponse.data;

    // Then get the responses
    const responsesData = await getDemographicResponses(surveyResponseId);

    // Transform the data similar to old repo
    const { section } = demographicsData;

    // Create a map of responses by question ID
    const responseMap = new Map();
    if (responsesData.results) {
      responsesData.results.forEach((response: any) => {
        responseMap.set(response.question, response);
      });
    }

    // Create a map of comment responses by question ID for QuestionInput types
    const commentMap = new Map();
    if (responsesData.comment_responses) {
      responsesData.comment_responses.forEach((comment: any) => {
        commentMap.set(comment.question, comment);
      });
    }

    // Transform components (questions)
    const questions: SurveyQuestion[] = (section.components || [])
      .filter((component: any) => component.resourcetype !== "QuestionNumber")
      .map((component: any) => {
        // For QuestionInput types, use comment responses instead of question responses
        let response;
        if (component.resourcetype === 'QuestionInput') {
          response = commentMap.get(component.id);
        } else {
          response = responseMap.get(component.id);
        }

        // Get comment data for questions that collect feedback
        const comment = commentMap.get(component.id);

        return {
          id: component.id,
          title: component.label,
          label: component.label,
          type: component.resourcetype,
          resourcetype: component.resourcetype,
          section: section.id,
          mandatory: component.mandatory,
          collect_feedback: component.collect_feedback,
          is_reverse_scale: component.is_reverse_scale,
          options: component.options || {},
          response: response ? {
            id: response.id,
            value: response.value,
            is_valid: response.is_valid,
          } : undefined,
          // Add comment data for questions that collect feedback
          feedback: comment?.value,
          commentId: comment?.id,
        };
      });

    // Return demographics section
    const demographicsSection: SurveySection = {
      id: section.id,
      title: section.name,
      questions,
      isDemographic: true,
    };

    return {
      questions,
      sections: [demographicsSection],
    };
  } catch (error) {
    console.error('Error fetching demographics data:', error);
    throw error;
  }
};

/**
 * Submit the complete survey
 */
export const submitSurvey = async (responseId: string): Promise<void> => {
  await axiosInstance.post(`/survey/survey-response/${responseId}/submit/`);
};
