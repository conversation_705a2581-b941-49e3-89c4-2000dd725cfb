/* eslint-disable @typescript-eslint/no-explicit-any */
import axiosInstance from '@/lib/axios';

// Utility function to format date to match legacy app format: "Jul 21, 2025, 07:03 PM"
const formatLastModified = (dateString: string): string => {
  if (!dateString) return 'Just now';

  try {
    const date = new Date(dateString);

    // Check if date is valid
    if (isNaN(date.getTime())) {
      return 'Just now';
    }

    // Format to match: "Jul 21, 2025, 07:03 PM"
    const options: Intl.DateTimeFormatOptions = {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    };

    return date.toLocaleDateString('en-US', options);
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Just now';
  }
};

export interface SurveyMeta {
  title: string;
  surveyFor: string;
  endDate: string;
  lastModified: string;
  canSubmit: boolean;
  nextTitle: string;
  buttonType: string;
  hideBack: boolean;
  indexId: string;
}

export interface Section {
  id: number;
  title: string;
  completed: boolean;
}

export interface Question {
  // SurveyQuestion interface fields (for compatibility with QuestionRenderer)
  id: string;
  title: string;
  label: string;
  type: string;
  resourcetype: string;
  section: number;
  mandatory: boolean;
  collect_feedback: boolean;
  is_reverse_scale: boolean;
  options?: any;
  response?: {
    id: string;
    value: any;
    is_valid: boolean;
  };

  // Legacy fields for backward compatibility
  feedback?: string;
  hasFeedback: boolean;
  responseId?: string;
  commentId?: string;
  categoryID?: number;
  isMandatory?: boolean;
  hasSwap?: boolean;
}

export interface Analytics {
  total: number;
  completed: number;
  sections: number[];
}

export interface SurveyData {
  meta: SurveyMeta;
  sections: Section[];
  questions: Question[];
  analytics: Analytics;
}

// Transform API response to match the expected format
const transformSurveyResponse = (responsesData: any, _: any): SurveyData => {
  const { first_name, last_name } = responsesData?.pairing_detail?.target || {};

  // Extract sections from responsesData.survey_detail (similar to engagement survey)
  const surveyDetail = responsesData.survey_detail || {};
  const sections = surveyDetail.sections?.map((section: any) => ({
    id: section.id,
    title: section.name || section.title,
    completed: false // Will be calculated based on responses
  })) || [];

  // Create a map of responses by question ID
  const responseMap = new Map();
  if (responsesData.question_responses) {
    responsesData.question_responses.forEach((response: any) => {
      responseMap.set(response.question, response);
    });
  }

  // Create a map of comments by question ID
  const commentMap = new Map();
  if (responsesData.comment_responses) {
    responsesData.comment_responses.forEach((comment: any) => {
      commentMap.set(comment.question, comment);
    });
  }

  // Extract questions from sections.components (similar to engagement survey)
  const allQuestions: any[] = [];
  surveyDetail.sections?.forEach((section: any) => {
    if (section.components) {
      section.components.forEach((component: any) => {
        const response = responseMap.get(component.id);
        const comment = commentMap.get(component.id);



        // For QuestionInput types, the response data comes from comment responses
        const isQuestionInput = component.resourcetype === 'QuestionInput';

        allQuestions.push({
          id: component.id.toString(),
          title: component.label,
          label: component.label,
          type: component.resourcetype,
          resourcetype: component.resourcetype,
          section: section.id,
          mandatory: component.mandatory,
          collect_feedback: component.collect_feedback,
          is_reverse_scale: component.is_reverse_scale,
          options: component.options || {},
          response: isQuestionInput && comment ? {
            id: comment.id,
            value: comment.value,
            is_valid: true
          } : (response ? {
            id: response.id,
            value: response.value,
            is_valid: true
          } : undefined),
          // Keep the old interface for backward compatibility
          categoryID: section.id,
          isMandatory: component.mandatory,
          hasFeedback: component.collect_feedback,
          hasSwap: component.is_reverse_scale,
          responseId: isQuestionInput ? comment?.id : response?.id,
          feedback: comment?.value,
          commentId: comment?.id
        });
      });
    }
  });

  // Calculate analytics
  const completedQuestions = allQuestions.filter((q: any) => {
    // For QuestionInput types, check feedback instead of response
    const isQuestionInput = q.type === 'QuestionInput' || q.resourcetype === 'QuestionInput';

    if (isQuestionInput) {
      return q.feedback !== undefined && q.feedback !== null && q.feedback !== '';
    } else {
      return q.response !== undefined && q.response !== null && q.response !== '';
    }
  }).length;

  const completedSections = sections.filter((section: any) => {
    const sectionQuestions = allQuestions.filter((q: any) => q.categoryID === section.id);
    return sectionQuestions.length > 0 && sectionQuestions.every((q: any) => {
      // For QuestionInput types, check feedback instead of response
      const isQuestionInput = q.type === 'QuestionInput' || q.resourcetype === 'QuestionInput';

      if (isQuestionInput) {
        return q.feedback !== undefined && q.feedback !== null && q.feedback !== '';
      } else {
        return q.response !== undefined && q.response !== null && q.response !== '';
      }
    });
  }).map((s: any) => s.id);

  return {
    meta: {
      title: responsesData.index?.title || 'Survey',
      surveyFor: first_name && last_name ? `${first_name} ${last_name}` : 'Unknown',
      endDate: responsesData.index?.deadline || '',
      lastModified: formatLastModified(responsesData.updated_on),
      canSubmit: completedQuestions === allQuestions.length && allQuestions.length > 0,
      nextTitle: 'Next',
      buttonType: 'primary',
      hideBack: false,
      indexId: responsesData.index?.id || ''
    },
    sections: sections.map((section: any) => ({
      ...section,
      completed: completedSections.includes(section.id)
    })),
    questions: allQuestions,
    analytics: {
      total: allQuestions.length,
      completed: completedQuestions,
      sections: completedSections
    }
  };
};

export const upwardReviewService = {
  // Get survey response data
  getSurveyResponse: async (responseId: string): Promise<any> => {
    const response = await axiosInstance.get(`/survey/survey-response/${responseId}`);
    return response.data;
  },

  // Get survey structure data
  getSurveyData: async (surveyId: string): Promise<any> => {
    const response = await axiosInstance.get(`/survey/survey/${surveyId}`, {
      params: { survey: surveyId }
    });
    return response.data;
  },

  // Get complete survey data (combines response and structure)
  getCompleteSurveyData: async (responseId: string, surveyId: string): Promise<SurveyData> => {
    const [responsesData, surveyData] = await Promise.all([
      upwardReviewService.getSurveyResponse(responseId),
      upwardReviewService.getSurveyData(surveyId)
    ]);

    return transformSurveyResponse(responsesData, surveyData);
  },

  // Get FAQs for a survey
  getFAQs: async (surveyIndex: string): Promise<any[]> => {
    try {
      const response = await axiosInstance.get('/staff/customers/faq/', {
        params: { survey_index: surveyIndex }
      });
      return response.data || [];
    } catch (error) {
      console.warn('Failed to fetch FAQs:', error);
      return [];
    }
  },

  // Update question response (similar to engagement survey)
  updateQuestionResponse: async (responseId: string, questionId: string, response: any, existingResponseId?: string): Promise<any> => {
    const payload = {
      survey_response: responseId,
      question: questionId, // Use string UUID instead of number
      value: response,
      is_valid: true,
      feedback: ""
    };

    if (existingResponseId) {
      // Update existing response
      return axiosInstance.patch(`/survey/question-response/${existingResponseId}/`, payload);
    } else {
      // Create new response
      return axiosInstance.post('/survey/question-response/', payload);
    }
  },

  // Delete question response
  deleteQuestionResponse: async (responseId: string): Promise<void> => {
    await axiosInstance.delete(`/survey/question-response/${responseId}/`);
  },

  // Update comment response
  updateCommentResponse: async (responseId: string, questionId: string, comment: string, existingCommentId?: string): Promise<any> => {
    const payload = {
      survey_response: responseId,
      question: questionId, // Use string UUID instead of number
      value: comment,
      is_valid: true
    };

    if (existingCommentId) {
      // Update existing comment
      return axiosInstance.patch(`/survey/comment-response/${existingCommentId}/`, payload);
    } else {
      // Create new comment
      return axiosInstance.post('/survey/comment-response/', payload);
    }
  },

  // Submit survey
  submitSurvey: async (responseId: string): Promise<void> => {
    await axiosInstance.post(`/survey/survey-response/${responseId}/submit/`);
  }
};
